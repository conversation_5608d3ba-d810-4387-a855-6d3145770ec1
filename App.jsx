import React, { useState, useEffect, useRef } from 'react';
import { Space, message, Button, Modal, Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import '@ant-design/v5-patch-for-react-19';
import _ from 'lodash';
import { sliceContentByIds, getBaseUrl } from './utils/index.js';
import { useStore } from './stores/index.js';
import { useNavigate, BrowserRouter as Router, Routes, Route, Navigate, useLocation, matchPath, useParams } from 'react-router-dom';
// import { useAuth0 } from '@auth0/auth0-react'; // Assuming not used if custom /api/auth/me is primary

// Import components
import ArticleEditPage from './components/ArticleEditPage';
import ArticlePreviewPage from './components/ArticlePreviewPage';
import { LoadingComponent, LoginPage } from './components/Login';
import MainLayout from './components/MainLayout'; // Import MainLayout
import UnpdfTestPage from './components/UnpdfTestPage'; // Import UnpdfTestPage
import SearchTestPage from './components/SearchTestPage'; // Import SearchTestPage
import SaveCancelButtonsTestPage from './components/SaveCancelButtonsTestPage'; // Import SaveCancelButtonsTestPage
import HomeRedirect from './components/HomeRedirect'; // Import HomeRedirect

// Wrapper for ArticleEditPage to provide a stable key based on route parameters
const ArticleEditPageWithKey = () => {
  const { '*': doi, uuid: uuidFromParams } = useParams();
  // Ensure currentIdentifier is always a string for the key prop
  const currentIdentifier = uuidFromParams || doi || 'default-article-key';



  // ArticleEditPage will now fetch its own content and userChunkSize from the store
  return <ArticleEditPage key={currentIdentifier} />;
};

// Component for authenticated routes. This component's instance will be stable once rendered.
const AuthenticatedAppRoutes = () => {
  return (
    <Routes>
      <Route path="/article" element={<MainLayout />}>
        <Route index element={<ArticleEditPageWithKey />} />
      </Route>
      <Route path="/article/:uuid/edit" element={<MainLayout />}>
        <Route index element={<ArticleEditPageWithKey />} />
      </Route>
      <Route path="/doi/*" element={<MainLayout />}>
        <Route index element={<ArticleEditPageWithKey />} />
      </Route>
      <Route path="/unpdf-test" element={<MainLayout />}>
        <Route index element={<UnpdfTestPage />} />
      </Route>
      <Route path="/search-test" element={<MainLayout />}>
        <Route index element={<SearchTestPage />} />
      </Route>
      <Route path="/savecancel-test" element={<MainLayout />}>
        <Route index element={<SaveCancelButtonsTestPage />} />
      </Route>
      <Route path="/" element={<MainLayout />}>
        <Route index element={<HomeRedirect />} />
      </Route>
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

// Main content component handling login checks and routing logic
const AppContent = () => {
  const location = useLocation();
  const params = useParams(); // For accessing route parameters like :uuid or :*

  const user = useStore(state => state.user);
  const setUser = useStore(state => state.setUser);
  const setDefaultApiModel = useStore(state => state.setDefaultApiModel);
  const setSystemPrompt = useStore(state => state.setSystemPrompt);
  const setUserPrompt = useStore(state => state.setUserPrompt);

  const [isAuthCheckLoading, setIsAuthCheckLoading] = useState(true);
  const initialLoadPerformedRef = useRef(false); // Tracks if the initial set of parallel fetches has been initiated

  useEffect(() => {
    const isPreviewPath = matchPath("/article/:uuid", location.pathname);
    // Determine if we are on a path that requires fetching an article for editing
    const editArticleMatch = matchPath("/article/:uuid/edit", location.pathname);
    const editDoiMatch = matchPath("/doi/*", location.pathname);
    // Explicitly check for the "/article" path for new articles, to avoid fetching with 'default-article-key'
    const isNewArticlePath = location.pathname === "/article";

    if (isPreviewPath) {
      setIsAuthCheckLoading(false);
      initialLoadPerformedRef.current = true; // Mark as performed to avoid re-triggering
      return;
    }

    if (initialLoadPerformedRef.current) {
      // If initial load was done, only ensure loading spinner is off if it was on.
      // The user object changing will trigger re-renders and routing decisions, not re-fetches here.
      if (isAuthCheckLoading) setIsAuthCheckLoading(false);
      return;
    }

    setIsAuthCheckLoading(true);
    initialLoadPerformedRef.current = true;

    const abortController = new AbortController();
    const { signal } = abortController;

    const performInitialFetches = async () => {
      try {
        // 确定是否需要获取特定文章
        let articleIdForBootstrap = null;
        if (editArticleMatch && editArticleMatch.params.uuid) {
          articleIdForBootstrap = editArticleMatch.params.uuid;
        } else if (editDoiMatch && editDoiMatch.params['*']) {
          articleIdForBootstrap = editDoiMatch.params['*'];
        }

        // 使用新的合并API一次性获取所有启动数据
        const bootstrapData = await useStore.getState().fetchBootstrapData(articleIdForBootstrap, signal);

        if (bootstrapData && bootstrapData.user) {
          // 用户已登录，数据已通过 fetchBootstrapData 设置到store中
          console.log('[App] Bootstrap data loaded successfully');
        } else {
          // 用户未登录或获取失败
          setUser(null);
          console.log('User not logged in or bootstrap failed, aborting other dependent requests.');
          abortController.abort();
        }
      } catch (error) {
        if (error.name === 'AbortError') {
          console.log('Bootstrap fetch aborted');
        } else {
          console.error('Bootstrap fetch failed:', error);
          setUser(null);
        }
        // Abort others if bootstrap itself fails for network reasons etc.
        if (!signal.aborted) {
          abortController.abort();
        }
      } finally {
        // Primary auth check is done, this controls the main loading spinner for login page vs app
        setIsAuthCheckLoading(false);
      }
    };

    // --- Initiate bootstrap fetch ---
    performInitialFetches();

    // 注意：文章内容现在通过 fetchBootstrapData 一起获取，无需单独请求
    // 如果是新文章页面，清理内容状态
    if (isNewArticlePath) {
      useStore.getState().setContent([]);
      // Note: article state is managed by fetchBootstrapData
    }

    return () => {
      // 不要在编辑页面之间切换时中止请求
      // 只在真正离开编辑相关页面时才中止
      console.log('[AppContent] Cleanup called, current path:', location.pathname);
      // 暂时注释掉自动中止逻辑，让 useArticleDataFetching 自己管理请求
      // if (!signal.aborted) {
      //   abortController.abort();
      // }
    };
  }, [location.pathname, setUser, setDefaultApiModel, setSystemPrompt, setUserPrompt]);

  // 当登录状态未确定时（user === undefined），始终显示加载组件
  // 只有在明确知道用户已登录（user 为对象）或未登录（user === null）时才进行路由判断
  if (isAuthCheckLoading || user === undefined) {
    console.log('[AppContent] 显示 LoadingComponent - 登录状态检查中或状态未确定');
    return <LoadingComponent />;
  }

  // After loading and user state is determined (not undefined), decide to show LoginPage or AuthenticatedAppRoutes.
  return (
    <Routes>
      {/* Publicly accessible preview route */}
      <Route path="/article/:uuid" element={<ArticlePreviewPage />} />

      {/* All other routes are caught by "/*" */}
      <Route
        path="/*"
        element={
          user ? <AuthenticatedAppRoutes /> : <LoginPage /> // user will be null if not logged in, or user object if logged in
        }
      />
    </Routes>
  );
};

// Root App component, simply sets up the Router.
const App = () => {
  const isImportingPdf = useStore(state => state.isImportingPdf);
  const importProgressMessage = useStore(state => state.importProgressMessage);
  const cancelPdfImport = useStore(state => state.cancelPdfImport);
  const canRetryFinalizePdf = useStore(state => state.canRetryFinalizePdf);
  const triggerPdfImportRetry = useStore(state => state.triggerPdfImportRetry);
  const setShouldSkipAiParsing = useStore(state => state.setShouldSkipAiParsing);

  return (
    <Router>
      <div style={{ position: 'relative', height: 'auto', minHeight: '100vh' }}>
        <AppContent />
        <Modal
          open={isImportingPdf}
          title="PDF导入进度"
          footer={null}
          closable={false}
          centered
          maskClosable={false}
          width={480}
          styles={{
            body: { textAlign: 'center', padding: '24px' }
          }}
        >
          <div style={{ marginBottom: '24px' }}>
            <Spin
              indicator={<LoadingOutlined style={{ fontSize: 24, color: '#1890ff' }} spin />}
              style={{ marginRight: '12px' }}
            />
            <div style={{
              fontSize: '16px',
              color: '#262626',
              marginTop: '16px',
              lineHeight: '1.5'
            }}>
              {importProgressMessage || '正在处理导入，请稍候...'}
            </div>
          </div>

          <Space size="middle">
            <Button
              onClick={cancelPdfImport}
              type="default"
            >
              取消导入
            </Button>
            {/* 只在AI处理阶段显示跳过解析按钮 */}
            {importProgressMessage && (importProgressMessage.includes('AI处理') || importProgressMessage.includes('准备处理')) && (
              <Button
                onClick={() => {
                  console.log('跳过解析按钮被点击');
                  const storeState = useStore.getState();
                  console.log('当前导入状态:', {
                    isImportingPdf,
                    importProgressMessage,
                    currentImportController: !!storeState.currentImportController
                  });
                  console.log('当前导入状态的 controller.signal.aborted:', storeState.currentImportController?.signal?.aborted);
                  setShouldSkipAiParsing(true);
                  const controller = storeState.currentImportController;
                  if (controller && typeof controller.abort === 'function') {
                    console.log('调用 currentImportController.abort() 来中止AI解析');
                    controller.abort();
                  } else {
                    console.warn('无法调用 abort()：currentImportController 不存在或没有 abort 方法。');
                  }

                  console.log('设置shouldSkipAiParsing为true');
                  // 验证状态是否设置成功
                  setTimeout(() => {
                    const currentState = useStore.getState().shouldSkipAiParsing;
                    console.log('验证状态设置结果:', currentState);
                    console.log('当前完整store状态:', useStore.getState());
                  }, 100);
                }}
                type="default"
                style={{ backgroundColor: '#fff7e6', borderColor: '#ffd666', color: '#d48806' }}
              >
                跳过解析
              </Button>
            )}
            {canRetryFinalizePdf && (
              <Button
                onClick={triggerPdfImportRetry}
                type="primary"
              >
                重试
              </Button>
            )}
          </Space>
        </Modal>
      </div>
    </Router>
  );
};

export default App;
